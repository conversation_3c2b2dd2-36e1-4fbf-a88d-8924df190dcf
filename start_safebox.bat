@echo off
:: 设置UTF-8编码，避免中文乱码
chcp 65001 >nul 2>&1

:: -------------------------- 用户配置区 开始 --------------------------
:: 设置您的JDK安装路径
set "JAVA_HOME=D:\safebox\jdk"

:: 设置您要启动的SpringBoot应用的JAR包文件名
:: 如果脚本和jar包在同一目录，直接写文件名即可。否则，需要提供完整路径。
set "JAR_FILE=safebox.jar"

:: 设置SpringBoot应用的启动环境（Profile）
set "PROFILE=prod"

:: JVM参数配置
set "JVM_OPTS=-Xms128m -Xmx128m -XX:+UseG1GC"

:: 应用特定参数
set "APP_OPTS=--server.port=8080 --spring.profiles.active=%PROFILE% --spring.application.name=safebox"

:: 设置应用日志输出文件名。所有Java应用的打印输出（System.out和System.err）都会被写入此文件。
:: 注意：由于 cd 命令，此文件现在将始终与脚本和JAR在同一目录下创建。
set "LOG_FILE=startup.log"

:: -------------------------- 用户配置区 结束 --------------------------


:: ============================================================================
::                        下方为脚本逻辑，通常无需修改
:: ============================================================================

:: "%~dp0" 是一个特殊的批处理变量，它会自动扩展为当前正在执行的脚本所在的驱动器和路径，结尾带反斜杠。
:: /d 参数允许同时切换驱动器。
:: 这行代码确保了无论用户如何运行此脚本（双击、从startup快捷方式等），其当前工作目录都永远是脚本所在目录。
:: 这样，Java应用启动后，其 "./logs" 目录和 "%LOG_FILE%" 都会在此处正确创建。
cd /d "%~dp0"
set "JDK_PATH=%JAVA_HOME%\bin\java.exe"
:: =====================================================================

rem 检查JDK路径是否正确配置
if not exist %JDK_PATH% (
    echo [%date% %time%] - [错误] 指定的JDK路径不存在或配置错误! >> %LOG_FILE%
    echo [%date% %time%] - 请检查脚本中的 JDK_PATH 变量是否正确指向了 java.exe 文件。 >> %LOG_FILE%
    echo [%date% %time%] - 当前配置的路径: %JDK_PATH% >> %LOG_FILE%
    exit /b 1
)

rem 检查JAR包是否存在
if not exist "%JAR_FILE%" (
    echo [%date% %time%] - [错误] 找不到SpringBoot应用的JAR包文件! >> %LOG_FILE%
    echo [%date% %time%] - 请确保脚本和JAR文件在同一个目录，或检查 JAR_FILE 变量配置是否正确。 >> %LOG_FILE%
    echo [%date% %time%] - 正在查找的文件: %JAR_FILE% >> %LOG_FILE%
    exit /b 1
)

rem 静默启动Java应用

echo =========================[%date% %time%]========================= >> %LOG_FILE%
echo 正在静默启动 SpringBoot 应用... >> %LOG_FILE%
echo 工作目录: %CD% >> %LOG_FILE%
echo JDK: %JDK_PATH% >> %LOG_FILE%
echo JAR: %JAR_FILE% >> %LOG_FILE%
echo Profile: %PROFILE% (如果为空则表示未指定) >> %LOG_FILE%
echo 脚本输出日志将重定向到: %LOG_FILE% >> %LOG_FILE%
echo 应用自身日志将输出到工作目录的 ./logs/ 文件夹下 >> %LOG_FILE%
echo =========================[%date% %time%]========================= >> %LOG_FILE%

rem 根据PROFILE是否为空，来构建Java启动命令

:: 构建完整的启动命令
set FULL_COMMAND="%JDK_PATH% %JVM_OPTS% -Dfile.encoding=UTF-8 -Djava.awt.headless=true -jar %JAR_FILE% %APP_OPTS%"

start "Safebox密码管理系统" /b "%FULL_COMMAND%" >> nul 2>&1

echo [%date% %time%] - 应用启动完成 >> %LOG_FILE%

rem 脚本执行完毕，自身CMD窗口会正常退出
exit /b 0
