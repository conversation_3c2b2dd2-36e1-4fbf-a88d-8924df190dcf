# Safebox 启动脚本使用说明

## 脚本文件说明

### 1. start_safebox.bat
**启动脚本** - 启动Safebox应用
- 检查运行环境和依赖
- 记录Java进程PID到文件
- 提供详细的启动日志
- 支持重复启动检查

### 2. stop_safebox.bat
**停止脚本** - 停止Safebox应用
- 根据PID文件优雅停止Java进程
- 支持强制终止功能
- 自动清理PID文件
- 检查端口释放状态

### 3. status_safebox.bat
**状态检查脚本** - 检查应用运行状态
- 检查PID文件和进程状态
- 验证端口占用情况
- 测试HTTP连接可用性
- 显示综合运行状态

### 4. restart_safebox.bat
**重启脚本** - 重启Safebox应用
- 先停止再启动应用
- 确保完全停止后再启动
- 简化重启操作

## 配置说明

### JDK路径配置
在脚本顶部找到以下配置项，根据实际安装路径修改：

```batch
:: JDK路径配置 - 请根据实际安装路径修改
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_301"
:: 备用JDK路径，如果主路径不存在则尝试使用
set "JAVA_HOME_BACKUP=C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot"
```

**常见JDK路径：**
- Oracle JDK: `C:\Program Files\Java\jdk1.8.0_xxx`
- OpenJDK: `C:\Program Files\OpenJDK\jdk-8.0.xxx`
- Eclipse Adoptium: `C:\Program Files\Eclipse Adoptium\jdk-8.0.xxx-hotspot`

### Spring Boot环境配置
```batch
:: Spring Boot 环境配置
:: 可选值: dev, test, prod
set "SPRING_PROFILES_ACTIVE=prod"
```

**环境说明：**
- `dev`: 开发环境，详细日志，禁用缓存
- `test`: 测试环境，中等日志级别
- `prod`: 生产环境，精简日志，启用缓存

### 端口配置
```batch
:: 应用端口配置
set "SERVER_PORT=8080"
```

### JVM参数配置
```batch
:: JVM参数配置
set "JVM_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication"
```

**参数说明：**
- `-Xms512m`: 初始堆内存512MB
- `-Xmx1024m`: 最大堆内存1GB
- `-XX:+UseG1GC`: 使用G1垃圾收集器
- `-XX:+UseStringDeduplication`: 启用字符串去重

## 使用方法

### 基本操作

#### 启动应用
```batch
# 启动Safebox应用
start_safebox.bat
```

#### 停止应用
```batch
# 停止Safebox应用
stop_safebox.bat
```

#### 检查状态
```batch
# 查看应用运行状态
status_safebox.bat
```

#### 重启应用
```batch
# 重启Safebox应用
restart_safebox.bat
```

### 开机自启动设置

#### 方法一：使用Windows启动文件夹
1. 按 `Win + R` 打开运行对话框
2. 输入 `shell:startup` 并回车
3. 将 `start_safebox.bat` 复制到启动文件夹
4. 重启电脑验证

#### 方法二：使用任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为"计算机启动时"
4. 操作选择"启动程序"
5. 程序路径设置为 `start_safebox.bat` 的完整路径
6. 勾选"使用最高权限运行"

#### 方法三：使用注册表（高级用户）
1. 打开注册表编辑器 (regedit)
2. 导航到 `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run`
3. 新建字符串值，名称为 `Safebox`
4. 值设置为 `start_safebox.bat` 的完整路径

## 文件说明

### PID文件
- 位置: `D:\safebox\safebox.pid`
- 内容: Java进程的PID号
- 用途: 进程管理和状态检查
- 注意: 应用停止时会自动删除

### 日志文件

#### 启动日志
- 位置: `D:\safebox\logs\startup.log`
- 内容: 脚本执行过程和结果
- 用途: 排查启动/停止问题

#### 应用日志
- 位置: `D:\safebox\logs\safebox.log`
- 内容: 应用运行日志
- 用途: 监控应用状态

#### 安全日志
- 位置: `D:\safebox\logs\security.log`
- 内容: 安全相关操作记录
- 用途: 安全审计

## 故障排除

### 常见问题

#### 1. 找不到Java运行环境
**错误信息**: "未找到Java运行环境，请检查JDK路径配置"
**解决方法**: 
- 检查JDK是否正确安装
- 修改脚本中的 `JAVA_HOME` 路径
- 确保路径中包含 `bin\java.exe`

#### 2. 找不到JAR文件
**错误信息**: "未找到应用JAR文件"
**解决方法**:
- 执行 `mvn clean package` 构建项目
- 确保 `target` 目录下存在 `safebox-0.0.1-SNAPSHOT.jar`

#### 3. 端口被占用
**错误信息**: "端口已被占用"
**解决方法**:
- 修改脚本中的 `SERVER_PORT` 为其他端口
- 或者停止占用该端口的其他程序

#### 4. 中文乱码
**解决方法**:
- 脚本已设置UTF-8编码 (`chcp 65001`)
- 确保系统支持UTF-8编码
- 检查日志文件编码格式

### 调试方法

#### 1. 查看启动日志
```
type logs\startup.log
```

#### 2. 手动执行Java命令
从脚本中复制完整的Java启动命令，在命令行中手动执行

#### 3. 检查端口监听
```
netstat -an | find ":8080"
```

#### 4. 查看进程
```
tasklist | find "java"
```

## 安全注意事项

1. **权限控制**: 确保脚本和应用具有适当的文件系统权限
2. **网络安全**: 生产环境建议修改默认端口
3. **日志安全**: 定期清理和备份日志文件
4. **密码保护**: 确保配置文件中的敏感信息得到保护

## 更新和维护

### 应用更新
1. 停止当前运行的应用
2. 执行 `mvn clean package` 构建新版本
3. 重新启动应用

### 脚本更新
1. 备份当前配置
2. 更新脚本文件
3. 恢复个人配置
4. 测试启动

---

如有问题，请查看日志文件或联系系统管理员。
