2025-09-22 10:59:02.341 [OkHttp https://msg-frontier.feishu.cn/...] ERROR com.lark.oapi.ws.Listener - java.io.EOFException
2025-09-22 10:59:02.347 [OkHttp https://msg-frontier.feishu.cn/...] INFO  com.lark.oapi.ws.Client - disconnected to wss://msg-frontier.feishu.cn/ws/v2?fpid=493&aid=552564&device_id=7551925319650967556&access_key=e14c30978d81a9bee389b53259ffc728&service_id=33554678&ticket=925f37bd-eb99-417e-90a7-093e00c7e967 [conn_id=7551925319650967556]
2025-09-22 10:59:02.347 [OkHttp https://msg-frontier.feishu.cn/...] INFO  com.lark.oapi.ws.Client - start reconnecting...
2025-09-22 10:59:23.641 [OkHttp https://msg-frontier.feishu.cn/...] INFO  com.lark.oapi.ws.Client - trying to reconnect for the 1st time
2025-09-22 10:59:24.086 [OkHttp https://msg-frontier.feishu.cn/...] INFO  com.lark.oapi.ws.Listener - connected to wss://msg-frontier.feishu.cn/ws/v2?fpid=493&aid=552564&device_id=7552742592527777811&access_key=bdcb80cda4bfb241780e6e0a0e090dbe&service_id=33554678&ticket=c3ccbe92-b434-4fd0-89aa-dec5331ab18e [conn_id=7552742592527777811]
2025-09-22 13:52:10.541 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-22 13:52:10.548 [main] INFO  com.newnary.rpa.safebox.SafeboxApplication - Starting SafeboxApplication using Java 1.8.0_402 on DESKTOP-R078LDB with PID 28728 (D:\Workspace\IdeaProjects\safebox\target\classes started by yangzc in D:\Workspace\IdeaProjects\safebox)
2025-09-22 13:52:10.548 [main] INFO  com.newnary.rpa.safebox.SafeboxApplication - The following 1 profile is active: "dev"
2025-09-22 13:52:11.682 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-22 13:52:11.695 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-22 13:52:11.695 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-09-22 13:52:11.695 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-22 13:52:11.844 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-22 13:52:11.844 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1248 ms
2025-09-22 13:52:14.543 [OkHttp https://msg-frontier.feishu.cn/...] INFO  com.lark.oapi.ws.Listener - connected to wss://msg-frontier.feishu.cn/ws/v2?fpid=493&aid=552564&device_id=7552786990566473732&access_key=5e7c020341c754c1ded4d429fddbac2e&service_id=33554678&ticket=2f110d8c-1a07-49fb-8cbe-ed3b894a61e3 [conn_id=7552786990566473732]
2025-09-22 13:52:14.595 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-09-22 13:52:14.871 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-22 13:52:14.916 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-22 13:52:14.927 [main] INFO  com.newnary.rpa.safebox.SafeboxApplication - Started SafeboxApplication in 5.046 seconds (JVM running for 6.684)
2025-09-22 13:52:14.931 [main] INFO  com.newnary.rpa.safebox.SafeboxApplication - === Safebox 密码管理系统启动完成 ===
2025-09-22 13:52:40.431 [pool-1-thread-2] INFO  c.n.rpa.safebox.service.LarkInteractionService - [用户进入机器人单聊事件], data: {"chat_id":"oc_b64111fc2044f42d6ea85af7719260af","operator_id":{"open_id":"ou_8254cf6ba62c6eb5bbbd1705d5ec0d91","union_id":"on_03ce4fc85ac2a99b68a0e9ffdbdcfcc6"},"last_message_id":"om_x100b43e4deb5cd1c0f42453e62aac1e","last_message_create_time":"1758267655821"}
