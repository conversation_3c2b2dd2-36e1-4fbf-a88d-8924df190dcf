package com.newnary.rpa.safebox.controller;

import com.newnary.rpa.safebox.common.BaseResponse;
import com.newnary.rpa.safebox.service.LarkInteractionService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 首页控制器
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 */
@Controller
public class HomeController {

    @Resource
    private LarkInteractionService larkInteractionService;

    /**
     * 显示主页面
     *
     * @return 主页面模板
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/web/";
    }

    @GetMapping("/web")
    public String home() {
        return "index";
    }

    @GetMapping("getLarkUsers")
    public @ResponseBody BaseResponse<Map<String, String>> getLarkUsers() {
        return BaseResponse.success(larkInteractionService.getUserMap());
    }

}
